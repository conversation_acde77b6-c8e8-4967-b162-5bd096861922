import styles from "./layout.module.scss";
import type { Metadata } from "next";
import { SpeedInsights } from '@vercel/speed-insights/next';
import { Open_Sans, Special_Elite, Margarine } from 'next/font/google';
import { ToastContainer } from "react-toastify";
import { SessionProvider } from "next-auth/react";
import JC_Header from "./components/JC_Header/JC_Header";
import { auth } from "./auth";
import TempComingSoon from "./tempComingSoon";
import { headers } from 'next/headers';
import { shouldHideHeaderFooter } from './utils/pageConfig';
import { redirect } from 'next/navigation';

// Site Metadata
export const metadata: Metadata = {
    title: process.env.NAME,
    description: "Building Inspection site for AIMS Engineering.",
    robots: {
        index: false,
        follow: false,
        googleBot: {
            index: false,
            follow: false,
        },
    },
};

// Font
const openSans = Open_Sans({ subsets: ["latin"], variable: '--font-open-sans' });
const kaushanScript = Special_Elite({ weight: "400", subsets: ["latin"], variable: '--font-kaushan-script' });
const shadowsIntoLight = Margarine({ weight: "400", subsets: ["latin"], variable: '--title-font' });

// Site Root
export default async function Layout_Root(_: Readonly<{
    children: React.ReactNode;
}>) {
    // Get the current path to check if it's a demo page and if header/footer should be hidden
    const headersList = headers();
    const path = headersList.get('x-pathname') || headersList.get('x-invoke-path') || headersList.get('x-url') || '';
    console.log('Path:', path);
    // More robust path detection - also check the URL header and referer
    const url = headersList.get('x-url') || '';
    const referer = headersList.get('referer') || '';

    // Extract path from URL if available
    let currentPath = path;
    if (!currentPath && url) {
        try {
            currentPath = new URL(url).pathname;
        } catch (e) {
            currentPath = '';
        }
    }

    const isDemoPage = currentPath.startsWith('/demo');
    const hideHeaderFooter = shouldHideHeaderFooter(currentPath);

    const session = await auth();

    // Check if user is logged in, if not redirect to login page
    // Allow access to login, register, forgot password, reset password, and demo pages without authentication
    const allowedPagesWithoutAuth = ['/login', '/register', '/forgotPassword', '/resetPassword', '/contact', '/privacyPolicy'];
    const isAllowedPageWithoutAuth = allowedPagesWithoutAuth.some(allowedPath =>
        currentPath === allowedPath || currentPath.startsWith(`${allowedPath}/`)
    );

    // Additional check: if the URL contains 'login', consider it as login page
    const isOnLoginPage = currentPath === '/login' || currentPath.includes('/login') || url.includes('/login') || referer.includes('/login');

    // Debug logging to understand what's happening
    console.log('Layout Debug:', {
        path,
        currentPath,
        url,
        referer,
        session: !!session,
        isDemoPage,
        isAllowedPageWithoutAuth,
        isOnLoginPage
    });

    // Only redirect if we're not already on an allowed page and not logged in and not on login page
    if (!session && !isDemoPage && !isAllowedPageWithoutAuth && !isOnLoginPage) {
        console.log('Redirecting to login from:', currentPath);
        redirect('/login');
    }

    // Allow access to demo pages without authentication
    const showContent = true; // session || isDemoPage;

    return (
        <html lang="en">

            <body className={`${styles.rootMainContainer} ${openSans.variable} ${kaushanScript.variable} ${shadowsIntoLight.variable}`} id="rootMainContainer">

                <div className={styles.mainLayout}>
                    {showContent && !hideHeaderFooter && <JC_Header />}

                    {showContent && <div className={styles.pageContainer}>
                        <SessionProvider session={session}>
                            {_.children}
                        </SessionProvider>
                    </div>}
                </div>

                {!showContent && <TempComingSoon />}

                <ToastContainer />

                <SpeedInsights />

            </body>

        </html>
  );
}
